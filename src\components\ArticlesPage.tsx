import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { 
  Search, 
  Filter, 
  Calendar, 
  User, 
  Clock, 
  Heart,
  MessageCircle,
  Share2,
  BookOpen,
  Tag,
  ChevronRight,
  Grid,
  List,
  TrendingUp,
  Star
} from 'lucide-react';

// Types
interface Article {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar?: string;
    title?: string;
  };
  category: {
    id: string;
    name: string;
    color: string;
  };
  tags: string[];
  publishedAt: string;
  readTime: number;
  views: number;
  likes: number;
  comments: number;
  featured: boolean;
  imageUrl?: string;
}

interface Category {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: any;
  articleCount: number;
}

const ArticlesPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  // State
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'latest' | 'popular' | 'trending'>('latest');
  const [showFilters, setShowFilters] = useState(false);

  // Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
  const mockCategories: Category[] = [
    {
      id: 'islamic-guidance',
      name: 'الإرشاد الإسلامي',
      description: 'مقالات حول الآداب الإسلامية في الزواج والتعارف',
      color: 'from-emerald-500 to-emerald-600',
      icon: BookOpen,
      articleCount: 15
    },
    {
      id: 'marriage-tips',
      name: 'نصائح الزواج',
      description: 'نصائح عملية للحياة الزوجية السعيدة',
      color: 'from-rose-500 to-rose-600',
      icon: Heart,
      articleCount: 12
    },
    {
      id: 'family-guidance',
      name: 'التوجيه الأسري',
      description: 'دور الأهل في اختيار شريك الحياة',
      color: 'from-blue-500 to-blue-600',
      icon: User,
      articleCount: 8
    },
    {
      id: 'digital-safety',
      name: 'الأمان الرقمي',
      description: 'كيفية الحماية من المحتالين والمخاطر الرقمية',
      color: 'from-amber-500 to-amber-600',
      icon: Star,
      articleCount: 6
    }
  ];

  const mockArticles: Article[] = [
    {
      id: '1',
      title: 'آداب التعارف في الإسلام: دليل شامل للمقبلين على الزواج',
      excerpt: 'تعرف على الضوابط الشرعية والآداب الإسلامية في التعارف قبل الزواج وكيفية اختيار الشريك المناسب وفقاً لتعاليم الإسلام الحنيف.',
      content: '',
      author: {
        name: 'د. أحمد الشريف',
        title: 'دكتور في الشريعة الإسلامية'
      },
      category: {
        id: 'islamic-guidance',
        name: 'الإرشاد الإسلامي',
        color: 'from-emerald-500 to-emerald-600'
      },
      tags: ['آداب إسلامية', 'التعارف', 'الزواج', 'الشريعة'],
      publishedAt: '2024-12-25',
      readTime: 8,
      views: 1250,
      likes: 89,
      comments: 23,
      featured: true,
      imageUrl: '/api/placeholder/600/300'
    },
    {
      id: '2',
      title: 'دور الأهل في اختيار شريك الحياة: التوازن بين الرأي والاختيار',
      excerpt: 'كيف يمكن للأهل المساعدة في عملية اختيار شريك الحياة مع احترام رغبة الأبناء وتوجيههم نحو الخيار الأفضل بحكمة ومحبة.',
      content: '',
      author: {
        name: 'أ. فاطمة النور',
        title: 'مستشارة أسرية'
      },
      category: {
        id: 'family-guidance',
        name: 'التوجيه الأسري',
        color: 'from-blue-500 to-blue-600'
      },
      tags: ['الأهل', 'الاختيار', 'التوجيه', 'الأسرة'],
      publishedAt: '2024-12-20',
      readTime: 6,
      views: 980,
      likes: 67,
      comments: 18,
      featured: true
    },
    {
      id: '3',
      title: 'الأمان الرقمي في مواقع الزواج: كيف تحمي نفسك من المحتالين',
      excerpt: 'نصائح مهمة للحفاظ على أمانك وخصوصيتك عند استخدام مواقع الزواج الإلكترونية وتجنب عمليات الاحتيال والمخاطر الرقمية.',
      content: '',
      author: {
        name: 'د. محمد العتيبي',
        title: 'خبير أمن معلومات'
      },
      category: {
        id: 'digital-safety',
        name: 'الأمان الرقمي',
        color: 'from-amber-500 to-amber-600'
      },
      tags: ['الأمان', 'الحماية', 'المحتالين', 'الخصوصية'],
      publishedAt: '2024-12-15',
      readTime: 10,
      views: 1450,
      likes: 102,
      comments: 31,
      featured: false
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    setLoading(true);
    setTimeout(() => {
      setCategories(mockCategories);
      setArticles(mockArticles);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter articles based on search and category
  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || article.category.id === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Sort articles
  const sortedArticles = [...filteredArticles].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.views - a.views;
      case 'trending':
        return b.likes - a.likes;
      case 'latest':
      default:
        return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime();
    }
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-slate-600">{t('articles.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50" dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-600 via-primary-700 to-emerald-600 text-white py-16 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-display">
              {t('articles.page.title')}
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto leading-relaxed">
              {t('articles.page.subtitle')}
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto relative">
              <div className="relative">
                <Search className={`absolute top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 ${isRTL ? 'right-4' : 'left-4'}`} />
                <input
                  type="text"
                  placeholder={t('articles.search.placeholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full ${isRTL ? 'pr-12 pl-4' : 'pl-12 pr-4'} py-4 rounded-2xl border-0 text-slate-800 placeholder-slate-500 focus:ring-2 focus:ring-white/20 focus:outline-none text-lg`}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-800 mb-4 font-display">
              {t('articles.categories.title')}
            </h2>
            <p className="text-xl text-slate-600">
              {t('articles.categories.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* All Articles Category */}
            <button
              onClick={() => setSelectedCategory('all')}
              className={`p-6 rounded-2xl border-2 transition-all duration-300 text-center group ${
                selectedCategory === 'all'
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-slate-200 hover:border-primary-300 hover:bg-slate-50'
              }`}
            >
              <div className={`w-12 h-12 mx-auto mb-4 rounded-xl flex items-center justify-center ${
                selectedCategory === 'all' ? 'bg-primary-500' : 'bg-slate-100 group-hover:bg-primary-100'
              }`}>
                <Grid className={`w-6 h-6 ${selectedCategory === 'all' ? 'text-white' : 'text-slate-600 group-hover:text-primary-600'}`} />
              </div>
              <h3 className={`font-bold text-lg mb-2 ${selectedCategory === 'all' ? 'text-primary-700' : 'text-slate-800'}`}>
                {t('articles.categories.all')}
              </h3>
              <p className="text-sm text-slate-600 mb-3">
                {t('articles.categories.allDescription')}
              </p>
              <span className={`text-sm font-medium ${selectedCategory === 'all' ? 'text-primary-600' : 'text-slate-500'}`}>
                {articles.length} {t('articles.categories.articlesCount')}
              </span>
            </button>

            {/* Category Cards */}
            {categories.map((category) => {
              const IconComponent = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`p-6 rounded-2xl border-2 transition-all duration-300 text-center group ${
                    selectedCategory === category.id
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-slate-200 hover:border-primary-300 hover:bg-slate-50'
                  }`}
                >
                  <div className={`w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-r ${category.color} flex items-center justify-center`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h3 className={`font-bold text-lg mb-2 ${selectedCategory === category.id ? 'text-primary-700' : 'text-slate-800'}`}>
                    {category.name}
                  </h3>
                  <p className="text-sm text-slate-600 mb-3">
                    {category.description}
                  </p>
                  <span className={`text-sm font-medium ${selectedCategory === category.id ? 'text-primary-600' : 'text-slate-500'}`}>
                    {category.articleCount} {t('articles.categories.articlesCount')}
                  </span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Filters and Controls */}
      <section className="py-8 bg-slate-50 border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Results Count */}
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-semibold text-slate-800">
                {t('articles.results.showing')} {sortedArticles.length} {t('articles.results.articles')}
                {selectedCategory !== 'all' && (
                  <span className="text-primary-600">
                    {' '}{t('articles.results.in')} {categories.find(c => c.id === selectedCategory)?.name}
                  </span>
                )}
              </h3>
            </div>

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* Sort Dropdown */}
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'latest' | 'popular' | 'trending')}
                  className="appearance-none bg-white border border-slate-300 rounded-lg px-4 py-2 pr-8 text-sm font-medium text-slate-700 hover:border-primary-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="latest">{t('articles.sort.latest')}</option>
                  <option value="popular">{t('articles.sort.popular')}</option>
                  <option value="trending">{t('articles.sort.trending')}</option>
                </select>
                <ChevronRight className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400 pointer-events-none" />
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center bg-white border border-slate-300 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid' ? 'bg-primary-500 text-white' : 'text-slate-600 hover:text-primary-600'
                  }`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list' ? 'bg-primary-500 text-white' : 'text-slate-600 hover:text-primary-600'
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Articles Grid/List */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {sortedArticles.length === 0 ? (
            <div className="text-center py-16">
              <BookOpen className="w-16 h-16 text-slate-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-slate-600 mb-2">
                {t('articles.noResults.title')}
              </h3>
              <p className="text-slate-500 mb-6">
                {t('articles.noResults.description')}
              </p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                }}
                className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
              >
                {t('articles.noResults.clearFilters')}
              </button>
            </div>
          ) : (
            <div className={viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
              : 'space-y-6'
            }>
              {sortedArticles.map((article) => (
                <ArticleCard
                  key={article.id}
                  article={article}
                  viewMode={viewMode}
                  isRTL={isRTL}
                  formatDate={formatDate}
                />
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

// Article Card Component
interface ArticleCardProps {
  article: Article;
  viewMode: 'grid' | 'list';
  isRTL: boolean;
  formatDate: (date: string) => string;
}

const ArticleCard: React.FC<ArticleCardProps> = ({ article, viewMode, isRTL, formatDate }) => {
  const { t } = useTranslation();

  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-slate-100">
        <div className="flex flex-col md:flex-row">
          {/* Image */}
          <div className="md:w-1/3 lg:w-1/4">
            <div className="h-48 md:h-full bg-gradient-to-br from-primary-100 to-emerald-100 flex items-center justify-center relative">
              {article.featured && (
                <div className="absolute top-4 left-4 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  <Star className="w-4 h-4 inline mr-1" />
                  {t('articles.featured')}
                </div>
              )}
              <div className="text-center">
                <div className={`w-16 h-16 mx-auto mb-2 rounded-xl bg-gradient-to-r ${article.category.color} flex items-center justify-center`}>
                  <BookOpen className="w-8 h-8 text-white" />
                </div>
                <p className="text-slate-600 text-sm">{t('articles.articleImage')}</p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6">
            <div className="flex flex-col h-full">
              {/* Category and Date */}
              <div className="flex items-center gap-4 mb-3">
                <span className={`px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${article.category.color} text-white`}>
                  {article.category.name}
                </span>
                <div className="flex items-center gap-2 text-sm text-slate-500">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(article.publishedAt)}</span>
                </div>
              </div>

              {/* Title */}
              <Link
                to={`/articles/${article.id}`}
                className="block group"
              >
                <h3 className="text-xl font-bold text-slate-800 mb-3 group-hover:text-primary-600 transition-colors line-clamp-2">
                  {article.title}
                </h3>
              </Link>

              {/* Excerpt */}
              <p className="text-slate-600 leading-relaxed mb-4 flex-1 line-clamp-3">
                {article.excerpt}
              </p>

              {/* Author and Stats */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-slate-200 to-slate-300 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-slate-600" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-800 text-sm">{article.author.name}</p>
                    <p className="text-xs text-slate-500">{article.author.title}</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 text-sm text-slate-500">
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>{article.readTime} {t('articles.readTime')}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="w-4 h-4" />
                    <span>{article.likes}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MessageCircle className="w-4 h-4" />
                    <span>{article.comments}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid View
  return (
    <article className="bg-white rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-slate-100 group">
      {/* Image */}
      <div className="h-48 bg-gradient-to-br from-primary-100 to-emerald-100 flex items-center justify-center relative">
        {article.featured && (
          <div className="absolute top-4 left-4 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            <Star className="w-4 h-4 inline mr-1" />
            {t('articles.featured')}
          </div>
        )}
        <div className="text-center">
          <div className={`w-16 h-16 mx-auto mb-2 rounded-xl bg-gradient-to-r ${article.category.color} flex items-center justify-center`}>
            <BookOpen className="w-8 h-8 text-white" />
          </div>
          <p className="text-slate-600 text-sm">{t('articles.articleImage')}</p>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Category and Date */}
        <div className="flex items-center justify-between mb-3">
          <span className={`px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${article.category.color} text-white`}>
            {article.category.name}
          </span>
          <div className="flex items-center gap-1 text-sm text-slate-500">
            <Calendar className="w-4 h-4" />
            <span>{formatDate(article.publishedAt)}</span>
          </div>
        </div>

        {/* Title */}
        <Link
          to={`/articles/${article.id}`}
          className="block"
        >
          <h3 className="text-xl font-bold text-slate-800 mb-3 group-hover:text-primary-600 transition-colors line-clamp-2">
            {article.title}
          </h3>
        </Link>

        {/* Excerpt */}
        <p className="text-slate-600 leading-relaxed mb-4 line-clamp-3">
          {article.excerpt}
        </p>

        {/* Author */}
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-slate-200 to-slate-300 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-slate-600" />
          </div>
          <div>
            <p className="font-medium text-slate-800 text-sm">{article.author.name}</p>
            <p className="text-xs text-slate-500">{article.author.title}</p>
          </div>
        </div>

        {/* Stats and Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
          <div className="flex items-center gap-4 text-sm text-slate-500">
            <div className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              <span>{article.readTime} {t('articles.readTime')}</span>
            </div>
            <div className="flex items-center gap-1">
              <Heart className="w-4 h-4" />
              <span>{article.likes}</span>
            </div>
            <div className="flex items-center gap-1">
              <MessageCircle className="w-4 h-4" />
              <span>{article.comments}</span>
            </div>
          </div>

          <Link
            to={`/articles/${article.id}`}
            className="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center gap-1 transition-colors"
          >
            {t('articles.readMore')}
            <ChevronRight className="w-4 h-4" />
          </Link>
        </div>

        {/* Tags */}
        {article.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {article.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-slate-100 text-slate-600 rounded-md text-xs font-medium"
              >
                #{tag}
              </span>
            ))}
            {article.tags.length > 3 && (
              <span className="px-2 py-1 bg-slate-100 text-slate-600 rounded-md text-xs font-medium">
                +{article.tags.length - 3}
              </span>
            )}
          </div>
        )}
      </div>
    </article>
  );
};

export default ArticlesPage;
