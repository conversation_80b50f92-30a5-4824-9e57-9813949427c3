import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ArrowLeft, 
  ArrowRight,
  Calendar, 
  Clock, 
  User, 
  Heart, 
  MessageCircle, 
  Share2, 
  BookOpen,
  Tag,
  Eye,
  ThumbsUp,
  Facebook,
  Twitter,
  Copy,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

// Types
interface Article {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    avatar?: string;
    title?: string;
    bio?: string;
  };
  category: {
    id: string;
    name: string;
    color: string;
  };
  tags: string[];
  publishedAt: string;
  updatedAt?: string;
  readTime: number;
  views: number;
  likes: number;
  comments: number;
  featured: boolean;
  imageUrl?: string;
}

interface Comment {
  id: string;
  author: {
    name: string;
    avatar?: string;
  };
  content: string;
  publishedAt: string;
  likes: number;
  replies?: Comment[];
}

const ArticleDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  
  // State
  const [article, setArticle] = useState<Article | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [relatedArticles, setRelatedArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [liked, setLiked] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [newComment, setNewComment] = useState('');

  // Mock article data
  const mockArticle: Article = {
    id: '1',
    title: 'آداب التعارف في الإسلام: دليل شامل للمقبلين على الزواج',
    excerpt: 'تعرف على الضوابط الشرعية والآداب الإسلامية في التعارف قبل الزواج وكيفية اختيار الشريك المناسب وفقاً لتعاليم الإسلام الحنيف.',
    content: `
      <h2>مقدمة</h2>
      <p>يعتبر الزواج في الإسلام نصف الدين، وهو سنة من سنن المرسلين، ولذلك فإن التعارف الذي يسبق الزواج له آداب وضوابط شرعية يجب مراعاتها لضمان بناء أسرة مسلمة قوية ومتماسكة.</p>
      
      <h2>الضوابط الشرعية للتعارف</h2>
      <p>هناك عدة ضوابط شرعية يجب مراعاتها عند التعارف للزواج:</p>
      <ul>
        <li><strong>وجود المحرم:</strong> يجب أن يكون التعارف في وجود محرم من أهل المرأة</li>
        <li><strong>الهدف الشرعي:</strong> يجب أن يكون الهدف من التعارف هو الزواج وليس مجرد التسلية</li>
        <li><strong>الحشمة والوقار:</strong> يجب المحافظة على الحشمة والوقار في التعامل</li>
        <li><strong>عدم الخلوة:</strong> تجنب الخلوة بين الرجل والمرأة</li>
      </ul>
      
      <h2>معايير اختيار الشريك</h2>
      <p>حدد الإسلام معايير واضحة لاختيار شريك الحياة:</p>
      <ol>
        <li><strong>الدين والخلق:</strong> وهو أهم المعايير كما قال النبي صلى الله عليه وسلم</li>
        <li><strong>التوافق الفكري:</strong> أن يكون هناك تفاهم وتوافق في الأفكار والمبادئ</li>
        <li><strong>القدرة على تحمل المسؤولية:</strong> خاصة للرجل في النفقة والرعاية</li>
        <li><strong>الصحة الجسدية والنفسية:</strong> لضمان حياة زوجية سليمة</li>
      </ol>
      
      <h2>دور الأهل في التعارف</h2>
      <p>للأهل دور مهم في عملية التعارف والزواج، حيث يقومون بـ:</p>
      <ul>
        <li>التوجيه والنصح</li>
        <li>البحث عن الشريك المناسب</li>
        <li>الاستخارة والدعاء</li>
        <li>المتابعة والإشراف على عملية التعارف</li>
      </ul>
      
      <h2>خاتمة</h2>
      <p>إن التزام الآداب الإسلامية في التعارف يضمن بناء أسرة قوية ومتماسكة، ويحقق البركة في الزواج. نسأل الله أن يوفق جميع الشباب المسلم لإيجاد الشريك المناسب وفقاً لتعاليم الإسلام.</p>
    `,
    author: {
      name: 'د. أحمد الشريف',
      title: 'دكتور في الشريعة الإسلامية',
      bio: 'دكتور في الشريعة الإسلامية من جامعة الأزهر، متخصص في فقه الأسرة والمعاملات، له عدة مؤلفات في مجال الفقه الإسلامي.'
    },
    category: {
      id: 'islamic-guidance',
      name: 'الإرشاد الإسلامي',
      color: 'from-emerald-500 to-emerald-600'
    },
    tags: ['آداب إسلامية', 'التعارف', 'الزواج', 'الشريعة', 'الأسرة'],
    publishedAt: '2024-12-25',
    updatedAt: '2024-12-26',
    readTime: 8,
    views: 1250,
    likes: 89,
    comments: 23,
    featured: true,
    imageUrl: '/api/placeholder/800/400'
  };

  const mockComments: Comment[] = [
    {
      id: '1',
      author: {
        name: 'أحمد محمد',
        avatar: '/api/placeholder/40/40'
      },
      content: 'مقال رائع ومفيد جداً، جزاك الله خيراً على هذه المعلومات القيمة.',
      publishedAt: '2024-12-26',
      likes: 12
    },
    {
      id: '2',
      author: {
        name: 'فاطمة علي',
        avatar: '/api/placeholder/40/40'
      },
      content: 'شكراً لك على التوضيح، هذا ما كنت أبحث عنه بالضبط.',
      publishedAt: '2024-12-25',
      likes: 8
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    setLoading(true);
    setTimeout(() => {
      if (id === '1') {
        setArticle(mockArticle);
        setComments(mockComments);
      }
      setLoading(false);
    }, 1000);
  }, [id]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleLike = () => {
    setLiked(!liked);
    // هنا سيتم إرسال الطلب للخادم
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title = article?.title || '';
    
    switch (platform) {
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`);
        break;
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`);
        break;
      case 'copy':
        navigator.clipboard.writeText(url);
        // إظهار رسالة نجاح
        break;
    }
    setShowShareMenu(false);
  };

  const handleCommentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      // إضافة التعليق الجديد
      const comment: Comment = {
        id: Date.now().toString(),
        author: {
          name: 'المستخدم الحالي'
        },
        content: newComment,
        publishedAt: new Date().toISOString(),
        likes: 0
      };
      setComments([comment, ...comments]);
      setNewComment('');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-slate-600">{t('articles.loading')}</p>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-slate-300 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-slate-600 mb-2">{t('articles.notFound.title')}</h2>
          <p className="text-slate-500 mb-6">{t('articles.notFound.description')}</p>
          <Link
            to="/articles"
            className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
          >
            {t('articles.notFound.backToArticles')}
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50" dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Navigation */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <button
            onClick={() => navigate('/articles')}
            className="flex items-center gap-2 text-slate-600 hover:text-primary-600 transition-colors"
          >
            {isRTL ? <ArrowRight className="w-5 h-5" /> : <ArrowLeft className="w-5 h-5" />}
            <span>{t('articles.backToArticles')}</span>
          </button>
        </div>
      </div>

      {/* Article Content */}
      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Article Header */}
        <header className="mb-8">
          {/* Category */}
          <div className="mb-4">
            <span className={`inline-block px-4 py-2 rounded-full text-sm font-medium bg-gradient-to-r ${article.category.color} text-white`}>
              {article.category.name}
            </span>
          </div>

          {/* Title */}
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-slate-800 mb-6 leading-tight font-display">
            {article.title}
          </h1>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-6 text-slate-600 mb-6">
            <div className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              <span>{formatDate(article.publishedAt)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              <span>{article.readTime} {t('articles.readTime')}</span>
            </div>
            <div className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              <span>{article.views.toLocaleString()} {t('articles.views')}</span>
            </div>
          </div>

          {/* Author */}
          <div className="flex items-center gap-4 p-6 bg-white rounded-2xl border border-slate-200">
            <div className="w-16 h-16 bg-gradient-to-r from-slate-200 to-slate-300 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-slate-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-bold text-slate-800 text-lg">{article.author.name}</h3>
              <p className="text-slate-600 mb-2">{article.author.title}</p>
              {article.author.bio && (
                <p className="text-sm text-slate-500 leading-relaxed">{article.author.bio}</p>
              )}
            </div>
          </div>
        </header>

        {/* Article Image */}
        <div className="mb-8">
          <div className="h-64 md:h-96 bg-gradient-to-br from-primary-100 to-emerald-100 rounded-2xl flex items-center justify-center">
            <div className="text-center">
              <div className={`w-20 h-20 mx-auto mb-4 rounded-xl bg-gradient-to-r ${article.category.color} flex items-center justify-center`}>
                <BookOpen className="w-10 h-10 text-white" />
              </div>
              <p className="text-slate-600">{t('articles.articleImage')}</p>
            </div>
          </div>
        </div>

        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-8">
          <div
            className="text-slate-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: article.content }}
          />
        </div>

        {/* Tags */}
        {article.tags.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">{t('articles.tags')}</h3>
            <div className="flex flex-wrap gap-3">
              {article.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-4 py-2 bg-slate-100 text-slate-700 rounded-full text-sm font-medium hover:bg-slate-200 transition-colors cursor-pointer"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Article Actions */}
        <div className="flex items-center justify-between p-6 bg-white rounded-2xl border border-slate-200 mb-8">
          <div className="flex items-center gap-4">
            <button
              onClick={handleLike}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                liked
                  ? 'bg-red-50 text-red-600 border border-red-200'
                  : 'bg-slate-50 text-slate-600 border border-slate-200 hover:bg-red-50 hover:text-red-600'
              }`}
            >
              <Heart className={`w-5 h-5 ${liked ? 'fill-current' : ''}`} />
              <span>{article.likes + (liked ? 1 : 0)}</span>
            </button>

            <div className="flex items-center gap-2 text-slate-600">
              <MessageCircle className="w-5 h-5" />
              <span>{comments.length} {t('articles.comments')}</span>
            </div>
          </div>

          <div className="relative">
            <button
              onClick={() => setShowShareMenu(!showShareMenu)}
              className="flex items-center gap-2 px-4 py-2 bg-slate-50 text-slate-600 border border-slate-200 rounded-lg hover:bg-slate-100 transition-colors"
            >
              <Share2 className="w-5 h-5" />
              <span>{t('articles.share')}</span>
            </button>

            {showShareMenu && (
              <div className="absolute top-full mt-2 right-0 bg-white border border-slate-200 rounded-lg shadow-lg p-2 z-10">
                <button
                  onClick={() => handleShare('facebook')}
                  className="flex items-center gap-3 w-full px-4 py-2 text-left hover:bg-slate-50 rounded-lg transition-colors"
                >
                  <Facebook className="w-5 h-5 text-blue-600" />
                  <span>Facebook</span>
                </button>
                <button
                  onClick={() => handleShare('twitter')}
                  className="flex items-center gap-3 w-full px-4 py-2 text-left hover:bg-slate-50 rounded-lg transition-colors"
                >
                  <Twitter className="w-5 h-5 text-blue-400" />
                  <span>Twitter</span>
                </button>
                <button
                  onClick={() => handleShare('copy')}
                  className="flex items-center gap-3 w-full px-4 py-2 text-left hover:bg-slate-50 rounded-lg transition-colors"
                >
                  <Copy className="w-5 h-5 text-slate-600" />
                  <span>{t('articles.copyLink')}</span>
                </button>
              </div>
            )}
          </div>
        </div>
      </article>

      {/* Comments Section */}
      <section className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        <div className="bg-white rounded-2xl border border-slate-200 p-6">
          <h2 className="text-2xl font-bold text-slate-800 mb-6">
            {t('articles.commentsSection')} ({comments.length})
          </h2>

          {/* Add Comment Form */}
          <form onSubmit={handleCommentSubmit} className="mb-8">
            <div className="mb-4">
              <label htmlFor="comment" className="block text-sm font-medium text-slate-700 mb-2">
                {t('articles.addComment')}
              </label>
              <textarea
                id="comment"
                rows={4}
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder={t('articles.commentPlaceholder')}
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
              />
            </div>
            <button
              type="submit"
              disabled={!newComment.trim()}
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 disabled:bg-slate-300 disabled:cursor-not-allowed transition-colors"
            >
              {t('articles.submitComment')}
            </button>
          </form>

          {/* Comments List */}
          <div className="space-y-6">
            {comments.length === 0 ? (
              <div className="text-center py-8">
                <MessageCircle className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                <p className="text-slate-500">{t('articles.noComments')}</p>
              </div>
            ) : (
              comments.map((comment) => (
                <div key={comment.id} className="border-b border-slate-100 pb-6 last:border-b-0">
                  <div className="flex items-start gap-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-slate-200 to-slate-300 rounded-full flex items-center justify-center flex-shrink-0">
                      <User className="w-5 h-5 text-slate-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold text-slate-800">{comment.author.name}</h4>
                        <span className="text-sm text-slate-500">{formatDate(comment.publishedAt)}</span>
                      </div>
                      <p className="text-slate-700 leading-relaxed mb-3">{comment.content}</p>
                      <div className="flex items-center gap-4">
                        <button className="flex items-center gap-1 text-sm text-slate-500 hover:text-primary-600 transition-colors">
                          <ThumbsUp className="w-4 h-4" />
                          <span>{comment.likes}</span>
                        </button>
                        <button className="text-sm text-slate-500 hover:text-primary-600 transition-colors">
                          {t('articles.reply')}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-slate-800 mb-8 text-center">
            {t('articles.relatedArticles')}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedArticles.slice(0, 3).map((relatedArticle) => (
              <Link
                key={relatedArticle.id}
                to={`/articles/${relatedArticle.id}`}
                className="bg-white rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-slate-100 group"
              >
                <div className="h-48 bg-gradient-to-br from-primary-100 to-emerald-100 flex items-center justify-center">
                  <div className="text-center">
                    <div className={`w-12 h-12 mx-auto mb-2 rounded-xl bg-gradient-to-r ${relatedArticle.category.color} flex items-center justify-center`}>
                      <BookOpen className="w-6 h-6 text-white" />
                    </div>
                    <p className="text-slate-600 text-sm">{t('articles.articleImage')}</p>
                  </div>
                </div>
                <div className="p-6">
                  <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${relatedArticle.category.color} text-white mb-3`}>
                    {relatedArticle.category.name}
                  </span>
                  <h3 className="text-lg font-bold text-slate-800 mb-2 group-hover:text-primary-600 transition-colors line-clamp-2">
                    {relatedArticle.title}
                  </h3>
                  <p className="text-slate-600 text-sm line-clamp-2 mb-4">
                    {relatedArticle.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-slate-500">
                    <span>{formatDate(relatedArticle.publishedAt)}</span>
                    <span>{relatedArticle.readTime} {t('articles.readTime')}</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </section>
      )}
    </div>
  );
};

export default ArticleDetailPage;
