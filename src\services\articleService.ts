import { supabase } from '../lib/supabase';

// Types
export interface Article {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author_id: string;
  category_id: string;
  tags: string[];
  published_at: string;
  updated_at?: string;
  read_time: number;
  views: number;
  likes: number;
  comments_count: number;
  featured: boolean;
  image_url?: string;
  status: 'draft' | 'published' | 'archived';
  created_at: string;
}

export interface ArticleCategory {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  article_count: number;
  created_at: string;
}

export interface ArticleComment {
  id: string;
  article_id: string;
  user_id: string;
  content: string;
  likes: number;
  parent_id?: string;
  created_at: string;
  updated_at?: string;
}

export interface ArticleWithDetails extends Article {
  author: {
    name: string;
    title?: string;
    bio?: string;
    avatar?: string;
  };
  category: {
    name: string;
    color: string;
  };
}

class ArticleService {
  // Get all articles with pagination and filters
  async getArticles(options: {
    page?: number;
    limit?: number;
    category?: string;
    search?: string;
    sortBy?: 'latest' | 'popular' | 'trending';
    status?: 'published' | 'draft' | 'archived';
  } = {}) {
    const {
      page = 1,
      limit = 12,
      category,
      search,
      sortBy = 'latest',
      status = 'published'
    } = options;

    let query = supabase
      .from('articles')
      .select(`
        *,
        author:users!articles_author_id_fkey(name, title, bio, avatar),
        category:article_categories!articles_category_id_fkey(name, color)
      `)
      .eq('status', status);

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category_id', category);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,excerpt.ilike.%${search}%,tags.cs.{${search}}`);
    }

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        query = query.order('views', { ascending: false });
        break;
      case 'trending':
        query = query.order('likes', { ascending: false });
        break;
      case 'latest':
      default:
        query = query.order('published_at', { ascending: false });
        break;
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching articles:', error);
      throw error;
    }

    return {
      articles: data as ArticleWithDetails[],
      totalCount: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
      currentPage: page
    };
  }

  // Get single article by ID
  async getArticleById(id: string): Promise<ArticleWithDetails | null> {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:users!articles_author_id_fkey(name, title, bio, avatar),
        category:article_categories!articles_category_id_fkey(name, color)
      `)
      .eq('id', id)
      .eq('status', 'published')
      .single();

    if (error) {
      console.error('Error fetching article:', error);
      return null;
    }

    // Increment view count
    await this.incrementViews(id);

    return data as ArticleWithDetails;
  }

  // Get article categories
  async getCategories(): Promise<ArticleCategory[]> {
    const { data, error } = await supabase
      .from('article_categories')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }

    return data || [];
  }

  // Get related articles
  async getRelatedArticles(articleId: string, categoryId: string, limit: number = 3): Promise<ArticleWithDetails[]> {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:users!articles_author_id_fkey(name, title, bio, avatar),
        category:article_categories!articles_category_id_fkey(name, color)
      `)
      .eq('category_id', categoryId)
      .eq('status', 'published')
      .neq('id', articleId)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching related articles:', error);
      return [];
    }

    return data as ArticleWithDetails[] || [];
  }

  // Increment article views
  async incrementViews(articleId: string): Promise<void> {
    const { error } = await supabase.rpc('increment_article_views', {
      article_id: articleId
    });

    if (error) {
      console.error('Error incrementing views:', error);
    }
  }

  // Like/Unlike article
  async toggleLike(articleId: string, userId: string): Promise<{ liked: boolean; totalLikes: number }> {
    // Check if user already liked the article
    const { data: existingLike } = await supabase
      .from('article_likes')
      .select('id')
      .eq('article_id', articleId)
      .eq('user_id', userId)
      .single();

    if (existingLike) {
      // Unlike
      await supabase
        .from('article_likes')
        .delete()
        .eq('article_id', articleId)
        .eq('user_id', userId);

      await supabase.rpc('decrement_article_likes', {
        article_id: articleId
      });

      const { data: article } = await supabase
        .from('articles')
        .select('likes')
        .eq('id', articleId)
        .single();

      return {
        liked: false,
        totalLikes: article?.likes || 0
      };
    } else {
      // Like
      await supabase
        .from('article_likes')
        .insert({
          article_id: articleId,
          user_id: userId
        });

      await supabase.rpc('increment_article_likes', {
        article_id: articleId
      });

      const { data: article } = await supabase
        .from('articles')
        .select('likes')
        .eq('id', articleId)
        .single();

      return {
        liked: true,
        totalLikes: article?.likes || 0
      };
    }
  }

  // Get article comments
  async getComments(articleId: string): Promise<ArticleComment[]> {
    const { data, error } = await supabase
      .from('article_comments')
      .select(`
        *,
        author:users!article_comments_user_id_fkey(name, avatar)
      `)
      .eq('article_id', articleId)
      .is('parent_id', null)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching comments:', error);
      return [];
    }

    return data || [];
  }

  // Add comment
  async addComment(articleId: string, userId: string, content: string, parentId?: string): Promise<ArticleComment | null> {
    const { data, error } = await supabase
      .from('article_comments')
      .insert({
        article_id: articleId,
        user_id: userId,
        content,
        parent_id: parentId
      })
      .select(`
        *,
        author:users!article_comments_user_id_fkey(name, avatar)
      `)
      .single();

    if (error) {
      console.error('Error adding comment:', error);
      return null;
    }

    // Increment comments count
    await supabase.rpc('increment_article_comments', {
      article_id: articleId
    });

    return data;
  }

  // Search articles
  async searchArticles(query: string, limit: number = 10): Promise<ArticleWithDetails[]> {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:users!articles_author_id_fkey(name, title, bio, avatar),
        category:article_categories!articles_category_id_fkey(name, color)
      `)
      .eq('status', 'published')
      .or(`title.ilike.%${query}%,excerpt.ilike.%${query}%,content.ilike.%${query}%`)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error searching articles:', error);
      return [];
    }

    return data as ArticleWithDetails[] || [];
  }

  // Get featured articles
  async getFeaturedArticles(limit: number = 3): Promise<ArticleWithDetails[]> {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:users!articles_author_id_fkey(name, title, bio, avatar),
        category:article_categories!articles_category_id_fkey(name, color)
      `)
      .eq('status', 'published')
      .eq('featured', true)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching featured articles:', error);
      return [];
    }

    return data as ArticleWithDetails[] || [];
  }
}

export const articleService = new ArticleService();
